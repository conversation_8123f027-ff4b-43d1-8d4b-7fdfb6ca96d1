import { useState, useEffect } from 'react';
import { Clock, Calendar, Zap, AlertCircle } from 'lucide-react';
import moment from 'moment';
import { Badge } from './badge';
import { paymentMatchService } from '../../services/paymentMatch';

interface NextMatchingCountdownProps {
  className?: string;
}

interface MatchingTimeData {
  is_auto_matching_enabled: boolean;
  mode?: string;
  frequency_hours?: number;
  next_run?: string;
  next_run_formatted?: string;
  is_due?: boolean;
  message?: string;
}

interface TimeRemaining {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  total: number;
}

export function NextMatchingCountdown({ className = '' }: NextMatchingCountdownProps) {
  const [matchingData, setMatchingData] = useState<MatchingTimeData | null>(null);
  const [timeRemaining, setTimeRemaining] = useState<TimeRemaining | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch matching time data
  const fetchMatchingTime = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await paymentMatchService.getNextMatchingTime();
      
      if (response.status === 'success' && response.data) {
        setMatchingData(response.data);
      } else {
        setError(response.message || 'Failed to fetch matching time');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch matching time');
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate time remaining using moment.js for proper timezone handling
  const calculateTimeRemaining = (): TimeRemaining | null => {
    if (!matchingData?.next_run) return null;

    const now = moment();
    const nextRun = moment(matchingData.next_run); // This will convert UTC to local time
    const difference = nextRun.diff(now);

    if (difference <= 0) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0, total: 0 };
    }

    const duration = moment.duration(difference);
    const days = Math.floor(duration.asDays());
    const hours = duration.hours();
    const minutes = duration.minutes();
    const seconds = duration.seconds();

    return { days, hours, minutes, seconds, total: difference };
  };

  // Update countdown every second
  useEffect(() => {
    if (!matchingData?.next_run) return;

    const updateCountdown = () => {
      const remaining = calculateTimeRemaining();
      setTimeRemaining(remaining);
    };

    updateCountdown(); // Initial calculation
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, [matchingData?.next_run]);

  // Fetch data on component mount
  useEffect(() => {
    fetchMatchingTime();
  }, []);

  // Format countdown display
  const formatCountdown = (time: TimeRemaining): string => {
    const parts = [];
    if (time.days > 0) parts.push(`${time.days}d`);
    if (time.hours > 0) parts.push(`${time.hours}h`);
    if (time.minutes > 0) parts.push(`${time.minutes}m`);
    if (time.seconds > 0 || parts.length === 0) parts.push(`${time.seconds}s`);
    
    return parts.slice(0, 3).join(' '); // Show max 3 units
  };

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
        <div className="flex items-center gap-2 text-gray-500">
          <Clock className="h-4 w-4 animate-spin" />
          <span className="text-sm">Loading matching schedule...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
        <div className="flex items-center gap-2 text-red-600">
          <AlertCircle className="h-4 w-4" />
          <span className="text-sm">{error}</span>
        </div>
      </div>
    );
  }

  if (!matchingData?.is_auto_matching_enabled) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
        <div className="flex items-center gap-2 text-gray-500">
          <Clock className="h-4 w-4" />
          <span className="text-sm">
            {matchingData?.message || 'Automatic matching is currently disabled'}
          </span>
        </div>
      </div>
    );
  }

  const isDue = matchingData.is_due || (timeRemaining && timeRemaining.total <= 0);
  const isInPast = timeRemaining && timeRemaining.total <= 0;

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
      <div className="space-y-3">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Zap className="h-4 w-4 text-brand-gold-600" />
            <span className="font-medium text-gray-900">Next Auto Matching</span>
          </div>
          {isDue && !isInPast && (
            <Badge className="bg-green-100 text-green-800 border-green-200">
              Due Now
            </Badge>
          )}
        </div>

        {/* Show different content based on time status */}
        {isInPast ? (
          // Show "coming soon" message when time is in the past
          <div className="bg-blue-50 rounded-lg p-3 border border-blue-200">
            <div className="text-center">
              <Clock className="h-5 w-5 text-blue-600 mx-auto mb-2" />
              <span className="text-sm font-medium text-blue-700">
                Next matching time will be available soon
              </span>
              <div className="text-xs text-blue-600 mt-1">
                The system is updating the schedule
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* Date and Time */}
            <div className="flex items-center gap-2 text-gray-600">
              <Calendar className="h-4 w-4" />
              <span className="text-sm">
                {moment(matchingData.next_run).format('MMM D, YYYY [at] h:mm A')}
              </span>
            </div>

            {/* Countdown */}
            {timeRemaining && (
              <div className="bg-brand-gold-50 rounded-lg p-3 border border-brand-gold-200">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-900">Time Remaining:</span>
                  <span className="text-lg font-mono font-bold text-brand-gold-600">
                    {formatCountdown(timeRemaining)}
                  </span>
                </div>
              </div>
            )}
          </>
        )}

        {/* Frequency Info */}
        <div className="text-xs text-gray-500">
          Runs every {matchingData.frequency_hours} hour{matchingData.frequency_hours !== 1 ? 's' : ''}
        </div>
      </div>
    </div>
  );
}
