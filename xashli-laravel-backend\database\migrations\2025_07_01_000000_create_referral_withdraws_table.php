<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('referral_withdraws', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id'); // The user who is withdrawing (referrer)
            $table->uuid('withdraw_id'); // The withdraw record this is associated with
            $table->decimal('amount', 20, 8); // Amount of referral bonus used in this withdrawal
            $table->string('currency'); // 'fiat' or 'crypto'
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('withdraw_id')->references('id')->on('withdraws')->onDelete('cascade');
            
            // Index for efficient queries
            $table->index(['user_id', 'created_at']);
            $table->index(['withdraw_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('referral_withdraws');
    }
};
