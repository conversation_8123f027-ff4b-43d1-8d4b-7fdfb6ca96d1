<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('referral_bonuses', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('referee_user_id');
            $table->uuid('referrer_user_id');
            $table->uuid('fund_id');
            $table->integer('level');
            $table->decimal('percentage', 5, 2);
            $table->decimal('amount', 20, 8);
            $table->timestamps();

            $table->foreign('referee_user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('referrer_user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('fund_id')->references('id')->on('funds')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('referral_bonuses');
    }
};
