import { apiClient } from './api';
import type { ApiResponse } from '../types/common';
import type { PaymentMatch } from '../types';

export const paymentMatchService = {
  /**
   * Get payment matches for the authenticated user
   */
  getPaymentMatches: async (params?: {
    status?: string;
    per_page?: number;
    page?: number;
  }): Promise<ApiResponse<PaymentMatch[]>> => {
    return apiClient.get<PaymentMatch[]>('/payment-matches', {
      params,
    });
  },

  /**
   * Get a specific payment match by ID
   */
  getPaymentMatch: async (id: string): Promise<ApiResponse<PaymentMatch>> => {
    return apiClient.get<PaymentMatch>(`/payment-matches/${id}`);
  },

  /**
   * Confirm payment sent for a payment match (funder side)
   */
  confirmPaymentSent: async (
    matchId: string,
    data: { transaction_hash: string }
  ): Promise<ApiResponse<PaymentMatch>> => {
    return apiClient.post<PaymentMatch>(
      `/payment-matches/${matchId}/confirm-payment-sent`,
      data
    );
  },

  /**
   * Confirm payment sent status (funder confirmation tracking)  /**
   * Confirm payment received (withdrawer side)
   */
  confirmPaymentReceived: async (
    matchId: string
  ): Promise<ApiResponse<PaymentMatch>> => {
    return apiClient.post<PaymentMatch>(
      `/payment-matches/${matchId}/confirm-payment-received`
    );
  },

  /**
   * Upload payment proof for a payment match
   */
  uploadPaymentProof: async (
    matchId: string,
    file: File
  ): Promise<ApiResponse<PaymentMatch>> => {
    const formData = new FormData();
    formData.append('payment_proof', file);

    return apiClient.post<PaymentMatch>(
      `/payment-matches/${matchId}/upload-payment-proof`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
  },

  /**
   * Get payment match statistics
   */
  getStatistics: async (params?: {
    start_date?: string;
    end_date?: string;
  }): Promise<ApiResponse<any>> => {
    return apiClient.get<any>('/payment-matches/statistics', {
      params,
    });
  },

  /**
   * Get next auto matching time information
   */
  getNextMatchingTime: async (): Promise<ApiResponse<{
    is_auto_matching_enabled: boolean;
    mode?: string;
    frequency_hours?: number;
    next_run?: string;
    next_run_formatted?: string;
    is_due?: boolean;
    message?: string;
  }>> => {
    return apiClient.get('/payment-matches/next-matching-time');
  },
};
