import { Link, useParams } from 'react-router-dom';
import {
  ArrowLeft,
  Calendar,
  Clock,
  CheckCircle,
  AlertCircle,
  FileText,
  Wallet,
  TrendingUp,
  CreditCard,
  Hash,
  BadgeCheck,
} from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import { Badge } from '../../components/ui';
import { NextMatchingCountdown } from '../../components/ui/NextMatchingCountdown';
import { WithdrawPaymentMatchCard } from '../../components/withdraws/WithdrawPaymentMatchCard';
import { useWithdraw } from '../../hooks/withdraws';
import toast from 'react-hot-toast';

export function WithdrawDetailsPage() {
  const { id } = useParams<{ id: string }>();
  const { withdraw, loading, error, confirmPaymentReceived } = useWithdraw(id!);

  const handleConfirmPaymentReceived = async (match: any) => {
    try {
      const success = await confirmPaymentReceived(match.id);
      if (success) {
        toast.success('Payment confirmed successfully!');
      }
    } catch (error) {
      toast.error('Failed to confirm payment. Please try again.');
    }
  };

  const handleDisputeCreated = (_dispute: any) => {
    toast.success(
      'Dispute created successfully! Our team will review your case.'
    );
    // You might want to refetch the withdraw data here to show the updated dispute status
  };

  const formatAmount = (amount: unknown, currency?: string) => {
    // Convert to number or default to 0
    const num = Number(amount) || 0;

    // Format based on currency type
    if (currency === 'crypto' || currency === 'SOL') {
      return `${num.toFixed(4)} SOL`;
    }
    // Default is Naira
    return `₦${Math.round(num).toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-warning/20 text-warning border-warning/30';
      case 'matched':
        return 'bg-primary/20 text-primary border-primary/30';
      case 'completed':
        return 'bg-success/20 text-success border-success/30';
      case 'paid':
        return 'bg-info/20 text-info border-info/30';
      case 'confirmed':
        return 'bg-success/20 text-success border-success/30';
      case 'disputed':
        return 'bg-destructive/20 text-destructive border-destructive/30';
      default:
        return 'bg-muted/20 text-muted-foreground border-muted/30';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className='h-4 w-4' />;
      case 'matched':
      case 'paid':
        return <AlertCircle className='h-4 w-4' />;
      case 'completed':
      case 'confirmed':
        return <CheckCircle className='h-4 w-4' />;
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className='container mx-auto px-6 py-8'>
        <div className='flex items-center justify-center min-h-[400px]'>
          <LoadingSpinner size='lg' />
        </div>
      </div>
    );
  }

  if (error || !withdraw) {
    return (
      <div className='container mx-auto px-6 py-8'>
        <div className='bg-background rounded-lg border border-border p-8 flex flex-col items-center'>
          <h2 className='text-xl font-medium text-foreground mb-4'>
            Withdraw Not Found
          </h2>
          <p className='text-foreground-secondary text-center mb-6'>
            The withdraw you're looking for could not be found or you don't have
            permission to view it.
          </p>
          <Link to='/withdraws'>
            <Button className='flex items-center gap-2'>
              <ArrowLeft className='h-4 w-4' />
              Back to Withdraws
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto px-6 py-8'>
      {/* Header with back button */}
      <div className='mb-8'>
        <Link
          to='/withdraws'
          className='inline-flex items-center text-foreground-secondary hover:text-foreground mb-6'
        >
          <ArrowLeft className='h-4 w-4 mr-2' />
          Back to withdraws
        </Link>

        <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
          <h1 className='text-3xl font-bold text-foreground'>
            Withdraw Details
          </h1>
          <Badge
            variant='outline'
            className={`${getStatusColor(withdraw.status)} flex items-center gap-1 px-3 py-1 text-sm`}
          >
            {getStatusIcon(withdraw.status)}
            <span className='capitalize'>{withdraw.status}</span>
          </Badge>
        </div>
      </div>

      {/* Main content */}
      <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
        {/* Left column - Withdraw info */}
        <div className='lg:col-span-2 space-y-6'>
          {/* Withdraw Details */}
          <div className='bg-background-secondary rounded-xl border border-border p-6'>
            <h2 className='text-xl font-semibold mb-4'>Withdraw Information</h2>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div>
                <p className='text-foreground-secondary mb-1 flex items-center gap-2'>
                  <Hash className='h-4 w-4 text-primary' />
                  Withdraw ID
                </p>
                <p className='font-medium text-foreground break-all'>
                  {`#${withdraw.id.substring(0, 8)}`}
                </p>
              </div>

              <div>
                <p className='text-foreground-secondary mb-1 flex items-center gap-2'>
                  <Calendar className='h-4 w-4 text-primary' />
                  Created At
                </p>
                <p className='font-medium'>{formatDate(withdraw.created_at)}</p>
              </div>

              <div>
                <p className='text-foreground-secondary mb-1 flex items-center gap-2'>
                  <TrendingUp className='h-4 w-4 text-success' />
                  Eligible Amount
                </p>
                <p className='font-semibold text-lg'>
                  {formatAmount(
                    withdraw.base_withdrawable_amount,
                    withdraw.fund?.currency
                  )}
                </p>
              </div>

              <div>
                <p className='text-foreground-secondary mb-1 flex items-center gap-2'>
                  <BadgeCheck className='h-4 w-4 text-success' />
                  Amount Matched
                </p>
                <p className='font-semibold text-lg'>
                  {formatAmount(
                    withdraw.amount_matched,
                    withdraw.fund?.currency
                  )}
                </p>
              </div>

              {withdraw.available_referral_bonus > 0 && (
                <div>
                  <p className='text-foreground-secondary mb-1'>
                    Eligible Referral Bonus
                  </p>
                  <p className='font-medium'>
                    {formatAmount(
                      withdraw.available_referral_bonus,
                      withdraw.fund?.currency
                    )}
                  </p>
                </div>
              )}

              {withdraw.withdrawable_referral_bonus > 0 && (
                <div>
                  <p className='text-foreground-secondary mb-1'>
                    Used Referral Bonus
                  </p>
                  <p className='font-medium'>
                    {formatAmount(
                      withdraw.withdrawable_referral_bonus,
                      withdraw.fund?.currency
                    )}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Fund Info */}
          {withdraw.fund && (
            <div className='bg-background-secondary rounded-xl border border-border p-6'>
              <h2 className='text-xl font-semibold mb-4'>Fund Information</h2>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div>
                  <p className='text-foreground-secondary mb-1 flex items-center gap-2'>
                    <Hash className='h-4 w-4 text-primary' />
                    Fund ID
                  </p>
                  <p className='font-medium text-foreground break-all'>
                    {`#${withdraw.fund_id.substring(0, 8)}`}
                  </p>
                </div>
                <div>
                  <p className='text-foreground-secondary mb-1 flex items-center gap-2'>
                    <Wallet className='h-4 w-4 text-primary' />
                    Fund Amount
                  </p>
                  <p className='font-semibold text-lg text-foreground'>
                    {formatAmount(withdraw.fund.amount, withdraw.fund.currency)}
                  </p>
                </div>
                <div>
                  <p className='text-foreground-secondary mb-1 flex items-center gap-2'>
                    <CreditCard className='h-4 w-4 text-primary' />
                    Currency
                  </p>
                  <p className='font-medium'>
                    {withdraw.fund.currency === 'fiat'
                      ? 'Fiat (₦)'
                      : 'Crypto (SOL)'}
                  </p>
                </div>
                <div>
                  <p className='text-foreground-secondary mb-1'>Fund Status</p>
                  <Badge
                    variant='outline'
                    className={`${getStatusColor(withdraw.fund.status)} inline-flex items-center gap-1 px-2 py-0.5`}
                  >
                    <span className='capitalize'>{withdraw.fund.status}</span>
                  </Badge>
                </div>
              </div>

              {withdraw.fund.id && (
                <div className='mt-4'>
                  <Link to={`/funds/${withdraw.fund.id}`}>
                    <Button variant='outline' className='w-full sm:w-auto'>
                      View Fund Details
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          )}

          {/* Payment Method */}
          {withdraw.payment_method && (
            <div className='bg-background-secondary rounded-xl border border-border p-6'>
              <h2 className='text-xl font-semibold mb-4'>Payment Method</h2>

              {withdraw.payment_method.type === 'bank' ? (
                <div className='flex items-center gap-4 p-4 bg-background-tertiary rounded-lg'>
                  <div className='w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center'>
                    <FileText className='h-5 w-5 text-primary' />
                  </div>
                  <div className='flex-1'>
                    <p className='font-medium text-foreground capitalize'>
                      {withdraw.payment_method.bank_name || 'Bank Account'}
                    </p>
                    <p className='text-sm text-foreground-secondary'>
                      {withdraw.payment_method.account_number
                        ? `${withdraw.payment_method.account_name || ''} - ${withdraw.payment_method.account_number}`
                        : 'No account details provided'}
                    </p>
                    <p className='text-xs text-foreground-secondary capitalize mt-1'>
                      {withdraw.payment_method.type} Payment
                    </p>
                  </div>
                </div>
              ) : (
                <div className='flex items-center gap-4 p-4 bg-background-tertiary rounded-lg'>
                  <div className='w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center'>
                    <Wallet className='h-5 w-5 text-primary' />
                  </div>
                  <div className='flex-1'>
                    <p className='font-medium text-foreground'>Crypto Wallet</p>
                    <p className='text-sm text-foreground-secondary font-mono break-all'>
                      {withdraw.payment_method.wallet_address ||
                        'No wallet address provided'}
                    </p>
                    <p className='text-xs text-foreground-secondary capitalize mt-1'>
                      {withdraw.payment_method.type} Payment
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Right column - Payment matches */}
        <div className='lg:col-span-1 space-y-6'>
          <div className='bg-background-secondary rounded-xl border border-border p-6'>
            <div className='mb-4'>
              <h2 className='text-xl font-semibold'>
                Payment Matches ({withdraw.payment_matches?.length || 0})
              </h2>
              <p className='text-sm text-foreground-secondary mt-1'>
                Funders will pay you
              </p>
            </div>

            {/* Next Matching Countdown */}
            <div className='mb-6'>
              <NextMatchingCountdown />
            </div>

            {withdraw.payment_matches && withdraw.payment_matches.length > 0 ? (
              <div className='space-y-4'>
                {withdraw.payment_matches.map((match, index) => (
                  <WithdrawPaymentMatchCard
                    key={match.id}
                    paymentMatch={match}
                    currency={withdraw.fund?.currency || 'fiat'}
                    formatAmount={formatAmount}
                    onConfirmPaymentReceived={handleConfirmPaymentReceived}
                    onDisputeCreated={handleDisputeCreated}
                    index={index + 1}
                  />
                ))}
              </div>
            ) : (
              <div className='bg-primary/5 border border-primary/20 rounded-lg p-4'>
                <div className='text-center'>
                  <h3 className='text-lg font-medium text-primary mb-2'>
                    No payment matches yet
                  </h3>
                  {withdraw.status === 'pending' && (
                    <p className='text-foreground-secondary text-sm'>
                      Your withdraw is waiting to be matched with available
                      funds.
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
