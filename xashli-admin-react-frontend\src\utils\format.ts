import moment from 'moment';
import type { Currency } from '../types/common';

/**
 * Format amounts for fiat and crypto currencies
 */
export function formatCurrencyAmount(
  amount: number | string | null | undefined,
  currency: Currency
): string {
  const num = Number(amount) || 0;

  return currency === 'fiat'
    ? `₦${Math.round(num).toLocaleString()}`
    : `${num.toFixed(4)} SOL`;
}

/**
 * Format large numbers with K, M, B suffixes
 */
export function formatCompactNumber(num: number | null | undefined): string {
  // Handle null, undefined, or invalid number values
  const numericValue = typeof num === 'number' && !isNaN(num) ? num : 0;

  return new Intl.NumberFormat('en-US', {
    notation: 'compact',
    maximumFractionDigits: 1,
  }).format(numericValue);
}

/**
 * Format date to readable string (converts UTC to local time)
 */
export function formatDate(date: string | Date): string {
  return moment(date).format('MMM D, YYYY');
}

/**
 * Format date and time to readable string (converts UTC to local time)
 */
export function formatDateTime(date: string | Date): string {
  return moment(date).format('MMM D, YYYY [at] h:mm A');
}

/**
 * Get relative time string (e.g., "2 hours ago") - handles UTC to local conversion
 */
export function getRelativeTime(date: string | Date): string {
  return moment(date).fromNow();
}

/**
 * Shorten ID for display in tables (shows first 8 characters)
 */
export function shortenId(id: string | null | undefined): string {
  if (!id) return 'N/A';
  return id.length > 8 ? `${id.substring(0, 8)}...` : id;
}
