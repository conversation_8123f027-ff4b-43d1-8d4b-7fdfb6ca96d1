import type { Currency } from './common';

export interface RefereeData {
  id: string;
  full_name: string;
  email: string;
  created_at: string;
  referee_count: number;
  is_active: boolean;
  referrer_id?: string;
}

export interface RefereesApiResponse {
  target_user: {
    id: string;
    full_name: string;
    email: string;
    referral_code: string;
  };
  referees: {
    level1: RefereeData[];
    level2: RefereeData[];
    level3: RefereeData[];
  };
  counts: {
    level_1: number;
    level_2: number;
    level_3: number;
    total: number;
  };
}

export interface ReferralInfoApiResponse {
  target_user: {
    id: string;
    full_name: string;
    email: string;
    referral_code: string;
  };
  referral_code: string;
  referee_count: number;
  referral_link: string;
}

export interface ReferralBonus {
  id: string;
  referrer_user_id: string;
  referee_user_id: string;
  fund_id: string;
  amount: number;
  level: 1 | 2 | 3;
  created_at: string;
  updated_at: string;
  referee_user: {
    id: string;
    full_name: string;
    email: string;
  };
  fund: {
    id: string;
    amount: number;
    currency: Currency;
  };
}

export interface BonusesApiResponse {
  bonuses: ReferralBonus[];
  summary: {
    fiat: {
      total_referral_bonus_amount: number;
      withdrawable_referral_amount: number;
      consumed_referral_amount: number;
      pending_referral_amount: number;
      count: number;
    };
    crypto: {
      total_referral_bonus_amount: number;
      withdrawable_referral_amount: number;
      consumed_referral_amount: number;
      pending_referral_amount: number;
      count: number;
    };
    total_count: number;
  };
}

export interface ReferralBonusesFilters {
  date_from?: string;
  date_to?: string;
  level?: 1 | 2 | 3;
}
