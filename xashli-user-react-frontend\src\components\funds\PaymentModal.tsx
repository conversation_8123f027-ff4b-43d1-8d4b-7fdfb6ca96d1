import { useState } from 'react';
import {
  X,
  DollarSign,
  CreditCard,
  Wallet,
  AlertCircle,
  Copy,
  Check,
  Phone,
  User,
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { useCopyToClipboard } from '../../utils/copy';
import { getTransactionDetails } from '../../utils/web3';
import { showToast } from '../../utils/toast';
import { formatCurrencyAmount } from '../../utils/format';
import type { PaymentMatch } from '../../types/paymentMatch';

interface PaymentConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (transactionHash: string) => Promise<void>;
  paymentMatch: PaymentMatch;
  fundCurrency: string;
  loading?: boolean;
}

export function PaymentModal({
  isOpen,
  onClose,
  onConfirm,
  paymentMatch,
  fundCurrency,
  loading = false,
}: PaymentConfirmationModalProps) {
  const [confirming, setConfirming] = useState(false);
  const [transactionHash, setTransactionHash] = useState('');
  const { copied, copy } = useCopyToClipboard();

  const amount = paymentMatch.amount || 0;
  const paymentMethod = paymentMatch.payment_method;

  const handleConfirm = async () => {
    setConfirming(true);

    try {
      // For crypto payments, verify transaction on blockchain
      if (paymentMethod?.type === 'crypto') {
        if (!transactionHash.trim()) {
          showToast.error('Transaction hash is required for crypto payments');
          return;
        }

        // Get transaction details from blockchain
        const txDetails = await getTransactionDetails(
          transactionHash.trim(),
          paymentMethod.crypto_network || 'solana'
        );

        if (!txDetails) {
          showToast.error('Transaction not found on blockchain');
          return;
        }

        if (txDetails.status !== 'success') {
          showToast.error('Transaction failed on blockchain');
          return;
        }

        // Verify recipient address matches
        if (
          txDetails.recipient.toLowerCase() !==
          paymentMethod.wallet_address?.toLowerCase()
        ) {
          showToast.error(
            'Recipient address does not match the payment method wallet'
          );
          return;
        }

        // Verify amount matches (with small tolerance for fees)
        const tolerance = 0.001;
        if (Math.abs(txDetails.amount - amount) > tolerance) {
          showToast.error(
            `Amount mismatch: sent ${txDetails.amount}, expected ${amount}`
          );
          return;
        }

        // Verify transaction is within 30 minutes
        const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
        if (txDetails.timestamp < thirtyMinutesAgo) {
          showToast.error('Transaction is older than 30 minutes');
          return;
        }

        await onConfirm(transactionHash.trim());
      } else {
        // For bank payments, generate mock transaction hash
        const timestamp = Date.now().toString(36);
        const randomStr = Math.random().toString(36).substring(2, 15);
        const mockHash = `tx_${timestamp}_${randomStr}`;
        await onConfirm(mockHash);
      }

      onClose();
    } catch (error) {
      console.error('Payment confirmation failed:', error);
      showToast.error('Payment verification failed. Please try again.');
    } finally {
      setConfirming(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 overflow-y-auto'>
      <div className='min-h-screen flex items-center justify-center p-4'>
        <div className='bg-background rounded-lg max-w-md w-full my-8 p-6'>
          {/* Header */}
          <div className='flex items-center justify-between mb-4'>
            <h2 className='text-lg font-semibold text-foreground'>
              Payment Details
            </h2>
            <Button
              variant='ghost'
              size='sm'
              onClick={onClose}
              disabled={confirming}
            >
              <X className='h-4 w-4' />
            </Button>
          </div>

          {/* Amount Section */}
          <div className='bg-background-secondary rounded-lg p-3 mb-3'>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <DollarSign className='h-4 w-4 text-primary' />
                <span className='text-sm font-medium text-foreground'>
                  Payment Amount
                </span>
              </div>
              <div className='text-right'>
                <div className='text-lg font-bold text-primary'>
                  {formatCurrencyAmount(amount, fundCurrency)}
                </div>
                <div className='text-xs text-foreground-secondary'>
                  ID: {paymentMatch.id.slice(0, 8)}...
                </div>
              </div>
            </div>
          </div>

          {/* Withdraw User Contact Information */}
          {paymentMatch.withdraw_user && (
            <div className='bg-background-secondary rounded-lg p-3 mb-3'>
              <div className='flex items-center gap-2 mb-3'>
                <User className='h-4 w-4 text-primary' />
                <span className='text-sm font-medium text-foreground'>
                  Recipient Information
                </span>
              </div>

              <div className='space-y-2'>
                {/* Display withdraw user's name (person to be funded) */}
                <div>
                  <label className='text-xs text-foreground-secondary'>
                    Full Name
                  </label>
                  <div className='text-sm font-medium text-foreground'>
                    {paymentMatch.withdraw_user.full_name}
                  </div>
                </div>

                {/* Display withdraw user's phone number (person to be funded) */}
                {paymentMatch.withdraw_user.phone && (
                  <div>
                    <label className='text-xs text-foreground-secondary'>
                      Phone Number
                    </label>
                    <div className='flex items-center gap-2 p-2 bg-background-tertiary rounded'>
                      <Phone className='h-3 w-3 text-foreground-secondary flex-shrink-0' />
                      <span className='text-sm font-medium text-foreground flex-1'>
                        {paymentMatch.withdraw_user.phone}
                      </span>
                      <button
                        onClick={() => {
                          if (paymentMatch.withdraw_user?.phone) {
                            copy(
                              paymentMatch.withdraw_user.phone,
                              'Phone number copied!',
                              'Failed to copy phone number'
                            );
                          }
                        }}
                        className='flex items-center justify-center w-6 h-6 rounded hover:bg-background-secondary transition-colors'
                        title='Copy phone number'
                      >
                        {copied ? (
                          <Check className='h-3 w-3 text-green-600' />
                        ) : (
                          <Copy className='h-3 w-3 text-foreground-secondary hover:text-foreground' />
                        )}
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Receiver Payment Method Details */}
          <div className='bg-background-secondary rounded-lg p-3 mb-4'>
            <div className='flex items-center gap-2 mb-3'>
              {paymentMethod?.type === 'bank' ? (
                <CreditCard className='h-4 w-4 text-primary' />
              ) : (
                <Wallet className='h-4 w-4 text-primary' />
              )}
              <span className='text-sm font-medium text-foreground'>
                Payment Details
              </span>
            </div>

            {paymentMethod ? (
              <div className='space-y-2'>
                {paymentMethod.type === 'bank' ? (
                  /* Bank Account Details */
                  <>
                    <div className='grid grid-cols-2 gap-2'>
                      <div>
                        <label className='text-xs text-foreground-secondary'>
                          Bank Name
                        </label>
                        <div className='text-sm font-medium text-foreground'>
                          {paymentMethod.bank_name}
                        </div>
                      </div>
                      <div>
                        <label className='text-xs text-foreground-secondary'>
                          Account Name
                        </label>
                        <div className='text-sm font-medium text-foreground'>
                          {paymentMethod.account_name}
                        </div>
                      </div>
                    </div>
                    <div>
                      <label className='text-xs text-foreground-secondary'>
                        Account Number
                      </label>
                      <div className='flex items-center gap-2 p-2 bg-background-tertiary rounded'>
                        <span className='text-sm font-mono font-medium text-foreground flex-1'>
                          {paymentMethod.account_number}
                        </span>
                        <button
                          onClick={() =>
                            paymentMethod.account_number &&
                            copy(
                              paymentMethod.account_number,
                              'Account number copied!',
                              'Failed to copy account number'
                            )
                          }
                          className='flex items-center justify-center w-6 h-6 rounded hover:bg-background-secondary transition-colors'
                          title='Copy account number'
                          disabled={!paymentMethod.account_number}
                        >
                          {copied ? (
                            <Check className='h-3 w-3 text-green-600' />
                          ) : (
                            <Copy className='h-3 w-3 text-foreground-secondary hover:text-foreground' />
                          )}
                        </button>
                      </div>
                    </div>
                  </>
                ) : (
                  /* Crypto Wallet Details */
                  <>
                    <div>
                      <label className='text-xs text-foreground-secondary'>
                        Network
                      </label>
                      <div className='text-sm font-medium text-foreground'>
                        {paymentMethod.crypto_network}
                      </div>
                    </div>
                    <div>
                      <label className='text-xs text-foreground-secondary'>
                        Wallet Address
                      </label>
                      <div className='flex items-center gap-2 p-2 bg-background-tertiary rounded'>
                        <span className='text-xs font-mono text-foreground break-all flex-1'>
                          {paymentMethod.wallet_address}
                        </span>
                        <button
                          onClick={() =>
                            paymentMethod.wallet_address &&
                            copy(
                              paymentMethod.wallet_address,
                              'Wallet address copied!',
                              'Failed to copy wallet address'
                            )
                          }
                          className='flex items-center justify-center w-6 h-6 rounded hover:bg-background-secondary transition-colors'
                          title='Copy wallet address'
                          disabled={!paymentMethod.wallet_address}
                        >
                          {copied ? (
                            <Check className='h-3 w-3 text-green-600' />
                          ) : (
                            <Copy className='h-3 w-3 text-foreground-secondary hover:text-foreground' />
                          )}
                        </button>
                      </div>
                    </div>

                    {/* Transaction Hash Input for Crypto */}
                    <div className='mt-4'>
                      <Input
                        label='Transaction Hash *'
                        type='text'
                        value={transactionHash}
                        onChange={e => setTransactionHash(e.target.value)}
                        placeholder='Enter transaction hash/signature'
                        disabled={confirming}
                        className='text-sm'
                      />
                      <p className='text-xs text-primary font-medium mt-1'>
                        Enter the transaction hash after sending the payment
                      </p>
                    </div>
                  </>
                )}
              </div>
            ) : (
              <div className='flex items-center gap-2 p-2 bg-warning/10 rounded border border-warning/20'>
                <AlertCircle className='h-4 w-4 text-warning flex-shrink-0' />
                <span className='text-sm text-warning'>
                  Payment method details not available
                </span>
              </div>
            )}
          </div>

          {/* Important Notice */}
          <div className='mb-4 p-2 bg-blue-50 dark:bg-blue-950/20 rounded border border-blue-200 dark:border-blue-800'>
            <div className='flex items-center gap-2'>
              <AlertCircle className='h-4 w-4 text-blue-600 dark:text-blue-400 flex-shrink-0' />
              <p className='text-sm text-blue-800 dark:text-blue-200'>
                {paymentMethod?.type === 'crypto'
                  ? 'After making your payment, enter the transaction hash and click "I\'ve Sent Payment".'
                  : 'After making your payment, click "I\'ve Sent Payment" to confirm.'}
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className='flex gap-3'>
            <Button
              variant='outline'
              className='flex-1'
              onClick={onClose}
              disabled={loading || confirming}
            >
              Cancel
            </Button>
            <Button
              className='flex-1'
              onClick={handleConfirm}
              disabled={
                loading ||
                confirming ||
                !paymentMethod ||
                (paymentMethod?.type === 'crypto' && !transactionHash.trim())
              }
            >
              {confirming ? (
                <>
                  <LoadingSpinner size='sm' className='mr-2' />
                  Confirming...
                </>
              ) : (
                "I've Sent Payment"
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
