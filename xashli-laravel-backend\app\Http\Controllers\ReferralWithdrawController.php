<?php

namespace App\Http\Controllers;

use App\Models\ReferralWithdraw;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ReferralWithdrawController extends Controller
{
    /**
     * Get referral withdraws.
     *
     * For users: Returns their own referral withdraws
     * For admins: Can specify user_id to get any user's referral withdraws, or get all if no user_id specified
     */
    public function index(Request $request): JsonResponse
    {
        $currentUser = auth()->user();
        $targetUserId = $currentUser->id;

        // If admin, allow querying other users' referral withdraws or all
        if ($currentUser->isAdmin()) {
            if ($request->has('user_id')) {
                $targetUser = User::find($request->user_id);
                if (!$targetUser) {
                    return $this->notFound('User not found');
                }
                $targetUserId = $targetUser->id;
            } else {
                // Ad<PERSON> wants all referral withdraws
                $targetUserId = null;
            }
        }

        $query = ReferralWithdraw::with(['user:id,full_name,email', 'withdraw:id,fund_id,status,created_at']);

        // Filter by user if specified
        if ($targetUserId) {
            $query->where('user_id', $targetUserId);
        }

        // Apply filters
        if ($request->has('currency') && in_array($request->currency, ['fiat', 'crypto'])) {
            $query->where('currency', $request->currency);
        }

        if ($request->has('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Order by most recent first
        $query->orderBy('created_at', 'desc');

        // Paginate results
        $perPage = min($request->get('per_page', 15), 50);
        $referralWithdraws = $query->paginate($perPage);

        // Calculate summary statistics
        $summaryQuery = ReferralWithdraw::query();
        if ($targetUserId) {
            $summaryQuery->where('user_id', $targetUserId);
        }

        $summary = [
            'total_count' => $summaryQuery->count(),
            'total_amount' => $summaryQuery->sum('amount'),
            'fiat' => [
                'count' => $summaryQuery->where('currency', 'fiat')->count(),
                'total_amount' => $summaryQuery->where('currency', 'fiat')->sum('amount'),
            ],
            'crypto' => [
                'count' => $summaryQuery->where('currency', 'crypto')->count(),
                'total_amount' => $summaryQuery->where('currency', 'crypto')->sum('amount'),
            ],
        ];

        $response = [
            'referral_withdraws' => $referralWithdraws->items(),
            'pagination' => [
                'current_page' => $referralWithdraws->currentPage(),
                'last_page' => $referralWithdraws->lastPage(),
                'per_page' => $referralWithdraws->perPage(),
                'total' => $referralWithdraws->total(),
            ],
            'summary' => $summary,
        ];

        // Add target user info if admin is querying specific user
        if ($currentUser->isAdmin() && $targetUserId && $request->has('user_id')) {
            $response['target_user'] = [
                'id' => $targetUser->id,
                'full_name' => $targetUser->full_name,
                'email' => $targetUser->email,
            ];
        }

        return $this->success($response, 'Referral withdraws retrieved successfully');
    }

    /**
     * Display the specified referral withdraw.
     */
    public function show(string $id): JsonResponse
    {
        $referralWithdraw = ReferralWithdraw::with([
            'user:id,full_name,email',
            'withdraw:id,fund_id,status,total_withdrawable_amount,created_at',
            'withdraw.fund:id,amount,currency,created_at'
        ])->find($id);

        if (!$referralWithdraw) {
            return $this->notFound('Referral withdraw not found');
        }

        // Check if the referral withdraw belongs to the authenticated user or if user is admin
        if ($referralWithdraw->user_id !== auth()->id() && !auth()->user()->isAdmin()) {
            return $this->forbidden('You do not have permission to view this referral withdraw');
        }

        return $this->success($referralWithdraw, 'Referral withdraw retrieved successfully');
    }
}
