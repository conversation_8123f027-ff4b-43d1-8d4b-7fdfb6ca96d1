<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ReferralBonus extends Model
{
    use HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'referee_user_id',
        'referrer_user_id',
        'fund_id',
        'level',
        'percentage',
        'amount',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'level' => 'integer',
        'percentage' => 'decimal:2',
        'amount' => 'decimal:8',
    ];

    /**
     * Get the referee user.
     */
    public function refereeUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'referee_user_id');
    }

    /**
     * Get the referrer user.
     */
    public function referrerUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'referrer_user_id');
    }

    /**
     * Get the fund for the referral bonus.
     */
    public function fund(): BelongsTo
    {
        return $this->belongsTo(Fund::class);
    }


}
